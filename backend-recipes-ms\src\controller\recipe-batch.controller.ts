import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeStepsStatus } from "../models/RecipeSteps";
import { RecipeResourceStatus } from "../models/RecipeResources";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug } from "../helper/slugGenerator";


// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const RecipeSteps = db.RecipeSteps;
const RecipeResources = db.RecipeResources;
const RecipeHistory = db.RecipeHistory;


import { createRecipeHistory } from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";


// ============================================================================
// CONSTANTS AND SHARED UTILITIES
// ============================================================================

// Constants for batch processing (kept for backward compatibility)
// Note: These constants are currently unused but kept for potential future use



// Common recipe validation
const validateRecipeAccess = async (recipeId: number, organizationId: string, transaction: any) => {
  return await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });
};

// Helper function to associate uploaded files with a recipe
const associateFilesWithRecipe = async (
  recipeId: number,
  resources: any[], // Can be array of item IDs or resource objects
  organizationId: string,
  userId: number,
  transaction: any
): Promise<{ success: boolean; updatedFiles: number; message: string }> => {
  if (!resources || resources.length === 0) {
    return {
      success: true,
      updatedFiles: 0,
      message: "No resources to associate"
    };
  }

  const recipeResourcesData = [];
  let validResourcesCount = 0;
  let skippedResourcesCount = 0;

  for (const resource of resources) {
    try {
      // Handle different input formats
      let resourceData: any = {};

      // Case 1: Simple item ID (number or string)
      if (typeof resource === 'number' || (typeof resource === 'string' && /^\d+$/.test(resource))) {
        const itemId = typeof resource === 'string' ? parseInt(resource, 10) : resource;
        resourceData = {
          type: "item",
          item_id: itemId,
          item_link: null,
          item_link_type: null
        };
      }
      // Case 2: Resource object with type and item_id
      else if (resource && typeof resource === 'object') {
        resourceData = {
          type: resource.type || "item",
          item_id: resource.item_id || null,
          item_link: resource.item_link || null,
          item_link_type: resource.item_link_type || null
        };
      }
      // Case 3: Invalid resource format
      else {
        skippedResourcesCount++;
        continue;
      }

      // Handle external links
      if (resourceData.type === "link" || resourceData.item_link) {
        // Determine item_link_type for external links
        let itemLinkType = "link";
        if (resourceData.item_link_type) {
          itemLinkType = resourceData.item_link_type;
        } else if (resourceData.item_link) {
          // Auto-detect link type from URL
          const url = resourceData.item_link.toLowerCase();
          if (url.includes("youtube.com")) {
            itemLinkType = "youtube";
          } else if (url.includes("vimeo.com") || url.includes(".mp4")) {
            itemLinkType = "video";
          } else if (url.includes("spotify.com") || url.includes(".mp3") || url.includes(".wav")) {
            itemLinkType = "audio";
          } else if (url.includes(".pdf")) {
            itemLinkType = "pdf";
          } else if (url.includes(".jpg") || url.includes(".png") || url.includes(".gif") || url.includes("imgur.com")) {
            itemLinkType = "image";
          } else if (url.includes(".doc") || url.includes(".txt")) {
            itemLinkType = "text";
          } else {
            itemLinkType = "other";
          }
        }

        recipeResourcesData.push({
          recipe_id: recipeId,
          type: "link",
          item_id: null,
          item_link: resourceData.item_link,
          item_link_type: itemLinkType,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
        validResourcesCount++;
      }
      // Handle uploaded files (item type)
      else if (resourceData.type === "item" || resourceData.item_id) {
        // Find the Item that belongs to the user's organization
        const item = await db.Item.findOne({
          where: {
            id: resourceData.item_id,
            item_organization_id: organizationId,
            item_status: "active"
          },
          transaction,
        });

        if (!item) {
          skippedResourcesCount++;
          continue; // Skip invalid items
        }

        // Determine item_link_type based on item type
        let itemLinkType;
        if (item.item_type === "image") {
          itemLinkType = "image";
        } else if (item.item_type === "video") {
          itemLinkType = "video";
        } else if (item.item_type === "audio") {
          itemLinkType = "audio";
        } else if (item.item_type === "pdf") {
          itemLinkType = "pdf";
        } else {
          itemLinkType = "document";
        }

        recipeResourcesData.push({
          recipe_id: recipeId,
          type: "item",
          item_id: item.id,
          item_link: null,
          item_link_type: itemLinkType,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
        validResourcesCount++;
      }
    } catch (error) {
      // Log error but continue processing other resources
      skippedResourcesCount++;
      continue;
    }
  }

  if (recipeResourcesData.length === 0) {
    return {
      success: false,
      updatedFiles: 0,
      message: `No valid resources found to associate. Processed: ${validResourcesCount}, Skipped: ${skippedResourcesCount}`
    };
  }

  try {
    // Create the RecipeResources records
    const createdResources = await RecipeResources.bulkCreate(recipeResourcesData, { transaction });

    return {
      success: true,
      updatedFiles: createdResources.length,
      message: `Successfully associated ${createdResources.length} resources with recipe. Processed: ${validResourcesCount}, Skipped: ${skippedResourcesCount}`
    };
  } catch (error) {
    return {
      success: false,
      updatedFiles: 0,
      message: `Failed to create recipe resources: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

// ============================================================================
// API 1: BASIC RECIPE INFORMATION
// Handles: basic recipe info, categories, dietary_attributes
// ============================================================================

/**
 * @description Handle the basic information step of recipe creation
 * @route POST /api/v1/recipes/batch/basic-info
 * @access Private
 * @functionality Creates new recipe with basic info, categories, and dietary attributes
 */
const createRecipeBasicInfo = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract basic recipe information fields
    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_complexity_level,
      categories,
      dietary_attributes
    } = sanitizedBody;


    const { id: userId, organization_id: organizationId } = req.user;

    // Validate required fields
    if (!recipe_title) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe title is required",
      });
    }

    // Generate unique slug from recipe title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existingRecipe = await Recipe.findOne({
        where: {
          recipe_slug: slug,
          organization_id: organizationId,
          recipe_status: {
            [Op.not]: RecipeStatus.deleted,
          },
        },
      });
      return !!existingRecipe;
    };

    // Generate unique slug
    const recipe_slug = await generateUniqueSlug(
      recipe_title,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Create main recipe with initial timestamps
    const currentTimestamp = new Date();
    const recipeData = {
      // Basic info
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility: has_recipe_public_visibility || false,
      has_recipe_private_visibility: has_recipe_private_visibility || false,
      recipe_status: recipe_status || RecipeStatus.draft,
      recipe_complexity_level,
      recipe_slug,
      ingredient_costs_updated_at: currentTimestamp,
      nutrition_values_updated_at: currentTimestamp,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    };

    const newRecipe = await Recipe.create(recipeData, { transaction });

    // Add categories if provided
    if (categories && Array.isArray(categories) && categories.length > 0) {
      const categoryData = categories.map((categoryId: number) => ({
        recipe_id: newRecipe.id,
        category_id: categoryId,
        status: "active",
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await db.RecipeCategory.bulkCreate(categoryData, { transaction });
    }



    // Create recipe attributes (optional for draft) - Type-wise handling
    const allAttributeData: any[] = [];

    // Handle dietary attributes (simple IDs)
    if (dietary_attributes && dietary_attributes.length > 0) {
      const dietaryData = dietary_attributes.map((attrId: number) => ({
        recipe_id: newRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        may_contain: false, // Required for unique constraint
        use_default: false, // Required for unique constraint
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...dietaryData);
    }
    // Bulk create all attributes if any exist
    if (allAttributeData.length > 0) {
      await RecipeAttributes.bulkCreate(allAttributeData, { transaction });
    }

    // Create recipe history entry for basic info
    await createRecipeHistory({
      recipe_id: newRecipe.id,
      action: RecipeHistoryAction.created,
      field_name: "recipe_basic_info",
      description: `Recipe "${recipe_title}" was created with basic information.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Recipe basic information saved successfully",
      data: {
        recipe_id: newRecipe.id,
        recipe_slug: newRecipe.recipe_slug,
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating recipe basic information"
    );
  }
};

// ============================================================================
// API 2: INGREDIENTS, NUTRITION & SERVING DETAILS
// Handles: ingredients, allergens, nutritions, HACCP data, cuisine attributes, serving details
// ============================================================================

/**
 * @description Handle ingredients, nutrition, cuisine type data and serving details for a recipe
 * @route POST /api/v1/recipes/batch/ingredients-nutrition
 * @access Private
 * @functionality Updates recipe with ingredients, nutrition, allergens, cuisine, HACCP data, and serving details
 */
const addIngredientsNutritionCuisine = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract ingredients, nutrition data, and serving details
    const {
      recipe_id,
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      haccp_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      // Serving details
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method
    } = sanitizedBody;




    const { id: userId, organization_id: organizationId } = req.user;

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    await Recipe.update(
      {
        is_ingredient_cooking_method:
          is_ingredient_cooking_method === "true" ||
          is_ingredient_cooking_method === true,
        is_preparation_method:
          is_preparation_method === "true" || is_preparation_method === true,
        is_cost_manual:
          is_cost_manual === "true" || is_cost_manual === true,
      },
      {
        where: { id: recipe_id },
        transaction,
      }
    );

    // Add ingredients if provided
    if (ingredients && ingredients.length > 0) {
      // Process each ingredient with upsert logic
      for (const ingredient of ingredients) {
        const ingredientData = {
          recipe_id,
          ingredient_id: ingredient.id,
          ingredient_quantity: ingredient.quantity,
          ingredient_measure: ingredient.measure,
          ingredient_wastage: ingredient.wastage,
          ingredient_cost: ingredient.cost,
          ingredient_cooking_method: ingredient.cooking_method,
          preparation_method: ingredient.preparation_method,
          recipe_ingredient_status: RecipeIngredientsStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        // Use upsert to update existing or create new
        await RecipeIngredients.upsert(ingredientData, { 
          transaction,
          fields: [
            'ingredient_quantity', 'ingredient_measure', 'ingredient_wastage', 
            'ingredient_cost', 'ingredient_cooking_method', 'preparation_method',
            'recipe_ingredient_status', 'updated_by', 'updated_at'
          ]
        });
      }

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(recipe_id, transaction);
    }

    // Add nutrition attributes if provided
    if (
      nutrition_attributes && nutrition_attributes.length > 0 ||
      allergen_attributes ||
      cuisine_attributes ||
      dietary_attributes ||
      haccp_attributes
    ) {
      // Process nutrition attributes
      if (nutrition_attributes && nutrition_attributes.length > 0) {
        for (const attr of nutrition_attributes) {
          const nutritionData = {
            recipe_id,
            attributes_id: attr.id,
            unit_of_measure: attr.unit_of_measure,
            unit: attr.unit,
            attribute_description: attr.attribute_description,
            use_default: attr.use_default || false,
            may_contain: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(nutritionData, { 
            transaction,
            fields: [
              'unit_of_measure', 'unit', 'attribute_description', 'use_default',
              'status', 'updated_by', 'updated_at'
            ]
          });
        }
      }

      // Process allergen "contains" attributes
      if (allergen_attributes?.contains && allergen_attributes.contains.length > 0) {
        for (const attrId of allergen_attributes.contains) {
          const allergenData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(allergenData, { 
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process allergen "may contain" attributes
      if (allergen_attributes?.may_contain && allergen_attributes.may_contain.length > 0) {
        for (const attrId of allergen_attributes.may_contain) {
          const allergenData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: true,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(allergenData, { 
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process cuisine attributes
      if (cuisine_attributes && cuisine_attributes.length > 0) {
        for (const attrId of cuisine_attributes) {
          const cuisineData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(cuisineData, { 
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process dietary attributes
      if (dietary_attributes && dietary_attributes.length > 0) {
        for (const attrId of dietary_attributes) {
          const dietaryData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(dietaryData, { 
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process HACCP attributes
      if (haccp_attributes && haccp_attributes.length > 0) {
        for (const attr of haccp_attributes) {
          const haccpData = {
            recipe_id,
            attributes_id: attr.id,
            attribute_description: attr.attribute_description,
            use_default: attr.use_default || false,
            may_contain: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(haccpData, { 
            transaction,
            fields: [
              'attribute_description', 'use_default', 'status', 
              'updated_by', 'updated_at'
            ]
          });
        }
      }

      // Update nutrition values timestamp
      await updateRecipeNutritionTimestamp(recipe_id, transaction);
    }

    // Update recipe with serving details
    const updateData: any = {
      updated_by: userId,
      updated_at: new Date()
    };

    // Add serving details if provided
    if (recipe_serve_in !== undefined) updateData.recipe_serve_in = recipe_serve_in;
    if (recipe_garnish !== undefined) updateData.recipe_garnish = recipe_garnish;
    if (recipe_head_chef_tips !== undefined) updateData.recipe_head_chef_tips = recipe_head_chef_tips;
    if (recipe_foh_tips !== undefined) updateData.recipe_foh_tips = recipe_foh_tips;
    if (recipe_impression !== undefined) updateData.recipe_impression = recipe_impression;
    if (recipe_yield !== undefined) updateData.recipe_yield = recipe_yield;
    if (recipe_yield_unit !== undefined) updateData.recipe_yield_unit = recipe_yield_unit;
    if (recipe_total_portions !== undefined) updateData.recipe_total_portions = recipe_total_portions;
    if (recipe_single_portion_size !== undefined) updateData.recipe_single_portion_size = recipe_single_portion_size;
    if (recipe_serving_method !== undefined) updateData.recipe_serving_method = recipe_serving_method;

    await recipe.update(updateData, { transaction });

    // Create recipe history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      field_name: "recipe_ingredients_nutrition",
      description: `Recipe "${recipe.recipe_title}" ingredients, nutrition, cuisine types, and serving details were updated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe ingredients, nutrition, cuisine data, and serving details saved successfully",
      data: { recipe_id },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();

    // Handle specific database constraint errors
    const sqlError = error as any;
    if (sqlError.name === 'SequelizeUniqueConstraintError' ||
      (sqlError.message && sqlError.message.includes('PRIMARY already exists'))) {

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Duplicate data detected. Please check for duplicate ingredients or attributes.",
        errorType: "DUPLICATE_ERROR",
        error: "Validation error",
      });
    }

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding ingredients, nutrition data, and serving details"
    );
  }
};

// ============================================================================
// API 4: RECIPE STEPS MANAGEMENT
// Handles: recipe steps creation and updates
// ============================================================================

/**
 * @description Add or update recipe steps
 * @route POST /api/v1/recipes/batch/steps
 * @access Private
 * @functionality Processes all recipe steps at once
 */
const addRecipeSteps = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract steps data
    const {
      recipe_id,
      recipe_steps
    } = sanitizedBody;

    
    const organizationId = req.user?.organization_id;
    const userId = req.user?.id;

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Validate steps data
    if (!Array.isArray(recipe_steps) || recipe_steps.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Steps array is required and must contain at least one step",
      });
    }

    // Make all existing steps inactive
    await RecipeSteps.update(
      { status: RecipeStepsStatus.inactive },
      {
        where: { recipe_id },
        transaction,
      }
    );

    // Create new step records (Fixed field mapping to match model)
    const stepsData = recipe_steps.map((step: any, index: number) => ({
      recipe_id,
      recipe_step_order: step.recipe_step_order || index + 1, // Fixed: use 'recipe_step_order' to match model
      recipe_step_description: step.recipe_step_description || step.recipe_step_description || "", // Fixed: use 'recipe_step_description' to match model
      item_id: step.item_id || null, // Optional item reference
      status: RecipeStepsStatus.active,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));

    await RecipeSteps.bulkCreate(stepsData, { transaction });

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      field_name: "recipe_steps",
      description: `Recipe "${recipe.recipe_title}" steps were updated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe steps saved successfully",
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding recipe steps"
    );
  }
};

// ============================================================================
// API 3: RECIPE FILE ASSOCIATION
// Handles: associating uploaded files with recipes
// ============================================================================

/**
 * @description Associate uploaded files with a recipe
 * @route POST /api/v1/recipes/batch/uploads
 * @access Private
 * @functionality Associates pre-uploaded Item records with a recipe by creating RecipeResources records
 */
const addRecipeUploads = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    // Parse form data
    const recipe_id = req.body.recipe_id;
    const recipeResourcesId = req.body.recipe_resources ? req.body.recipe_resources : [];
    const placeholderItemId = req.body.recipe_placeholder ? req.body.recipe_placeholder : null;

    // Validate recipe_id
    if (!recipe_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe ID is required",
      });
    }

    // Ensure recipeResourcesId is an array
    if (!Array.isArray(recipeResourcesId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "recipe_resources must be an array",
      });
    }

    const organizationId = req.user?.organization_id;
    const userId = req.user?.id;

     

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    if(recipeResourcesId.length > 0){
    // Make all existing resources inactiv
    await RecipeResources.update(
      { status: RecipeResourceStatus.inactive },
      {
        where: { recipe_id },
        transaction,
      }
    );

    // Associate pre-uploaded items with recipe (creates RecipeResources from Items)
    const associationResult = await associateFilesWithRecipe(
      recipe_id,
      recipeResourcesId,
      organizationId,
      userId,
      transaction
    );

    if (!associationResult.success) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: associationResult.message,
      });
    }

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      field_name: "recipe_files",
      description: `Recipe "${recipe.recipe_title}" files were associated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);
 }
   if(placeholderItemId){
     // Validate placeholder item exists and belongs to organization
     const placeholderItem = await db.Item.findOne({
       where: {
         id: placeholderItemId,
         item_organization_id: organizationId,
         item_status: "active"
       },
       transaction,
     });

     if (!placeholderItem) {
       return res.status(StatusCodes.BAD_REQUEST).json({
         status: false,
         message: "Placeholder item not found or not accessible",
       });
     }

     // Create recipe placeholder
     await Recipe.update({
      recipe_placeholder: placeholderItemId
     },{
      where: {
        id: recipe_id
      },
      transaction
     })

 }
    // Commit transaction
    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe files associated successfully",
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error associating recipe files"
    );
  }
};




// ============================================================================
// EXPORTS - FOCUSED BATCH API ENDPOINTS
// ============================================================================

/**
 * Refactored Recipe Batch Controller
 *
 * This controller has been organized into 7 focused API endpoints:
 *
 * 1. createRecipeBasicInfo - API 1: Basic recipe information, categories, dietary attributes
 * 2. addIngredientsNutritionCuisine - API 2: Ingredients, allergens, nutrition, HACCP data, cuisine attributes, serving details
 * 3. addRecipeSteps - API 4: Recipe steps management (simplified)
 * 4. addRecipeUploads - API 3: File association with recipes (simplified)
 * 5. uploadSingleFile - API 5: Single file upload using upload service - creates Item records and returns item_id
 * 6. deleteUploadedFile - API 6: Delete uploaded files by item_id
 * 7. bulkDeleteUploadedFiles - API 7: Bulk delete temporary files (for discard functionality)
 *
 * Key Features Maintained:
 * - All existing middleware (auth, validation, CORS, rate limiting)
 * - Same response formats and error handling patterns
 * - Transaction management and rollback capabilities
 * - File operation tracking and cleanup
 * - Backward compatibility with existing endpoints
 *
 * Improvements:
 * - Simplified APIs without complex batch logic
 * - Better separation of concerns
 * - Shared utility functions for common operations
 * - Consistent authorization and validation patterns
 * - Clear section organization with descriptive headers
 * - Reduced code duplication
 * - Individual file upload and delete capabilities
 * - Simplified file association process
 */
export default {
  createRecipeBasicInfo,
  addIngredientsNutritionCuisine,
  addRecipeSteps,
  addRecipeUploads
};
